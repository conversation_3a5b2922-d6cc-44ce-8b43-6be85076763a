import { useEffect, useRef } from "react"
import { useStores } from "@/models"
import { AppStackScreenProps } from "@/navigators"

interface UseProfileValidationProps {
  navigation: AppStackScreenProps<"Main">["navigation"]
}

/**
 * Hook to check if user has completed mandatory profile fields
 * and navigate to EditProfile if needed.
 */
export const useProfileValidation = ({ navigation }: UseProfileValidationProps) => {
  const { userProfileStore, authenticationStore } = useStores()
  const hasCheckedRef = useRef(false)

  useEffect(() => {
    // Only check once per session and only if user is authenticated
    if (hasCheckedRef.current || !authenticationStore.isAuthenticated) {
      return
    }

    // Only proceed if profile data has been loaded (userId is set)
    if (!userProfileStore.userId) {
      return
    }

    // Check if user has mandatory profile fields
    if (!userProfileStore.hasMandatoryFields) {
      hasCheckedRef.current = true
      // Navigate immediately on next tick to avoid flash of MainScreen
      setImmediate(() => {
        navigation.push("EditProfile", {})
      })
    } else {
      hasCheckedRef.current = true
    }
  }, [userProfileStore.hasMandatoryFields, userProfileStore.userId, authenticationStore.isAuthenticated, navigation])

  // Reset the check flag when user logs out
  useEffect(() => {
    if (!authenticationStore.isAuthenticated) {
      hasCheckedRef.current = false
    }
  }, [authenticationStore.isAuthenticated])
}
